import { StyleSheet, Text } from "react-native";
import { useNavigation } from "@react-navigation/native";
import { PartialProperty } from "../../interfaces/me";
import { Reservation, ReservationStatus } from "../../interfaces/reservation";
import { Card, Section } from "../main";
import { theme } from "../../theme/theme";
import { PROPERTY_SCREENS } from "../../navigation/constants";
import { PropertyStackNavigationProp } from "../../navigation/types";
import { Stats } from "../Stats";

interface ReservationsSectionProps {
  reservations: Reservation[];
  property: PartialProperty;
}

export const ReservationsSection: React.FC<ReservationsSectionProps> = ({
  reservations,
  property,
}) => {
  const navigation = useNavigation<PropertyStackNavigationProp>();

  if (!reservations?.length) {
    return (
      <Section title="Reservaciones">
        <Text style={styles.noDataText}>No hay reservaciones registradas</Text>
      </Section>
    );
  }

  // Separar por estado
  const pendingReservations = reservations.filter(
    (r) => r.status === ReservationStatus.PENDING
  );
  const approvedReservations = reservations.filter(
    (r) => r.status === ReservationStatus.APPROVED
  );
  const rejectedReservations = reservations.filter(
    (r) => r.status === ReservationStatus.REJECTED
  );

  return (
    <Section title="Reservaciones">
      <Card style={styles.summaryCard}>
        <Stats
          items={[
            {
              icon: "clock-outline",
              label: "Pendientes",
              value: pendingReservations.length,
              color: theme.colors.warning,
            },
            {
              icon: "check-circle",
              label: "Aprobadas",
              value: approvedReservations.length,
              color: theme.colors.success,
            },
            {
              icon: "close-circle",
              label: "Rechazadas",
              value: rejectedReservations.length,
              color: theme.colors.error,
            },
          ]}
          goTo={{
            label: "Ver reservaciones",
            onPress: () =>
              navigation.navigate(PROPERTY_SCREENS.PROPERTY_RESERVATIONS, {
                reservations,
                property,
              }),
          }}
        />
      </Card>
    </Section>
  );
};

const styles = StyleSheet.create({
  summaryCard: {
    flexDirection: "column",
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.md,
  },
  noDataText: {
    textAlign: "center",
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray500,
    padding: theme.spacing.lg,
    fontStyle: "italic",
  },
});
