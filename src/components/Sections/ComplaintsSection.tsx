import { StyleSheet, Text } from "react-native";
import { useNavigation } from "@react-navigation/native";
import { Complaint } from "../../interfaces/complaint";
import { Status } from "../../interfaces/maintenance-issue-report";
import { Card, Section } from "../main";
import { theme } from "../../theme";
import { PROPERTY_SCREENS } from "../../navigation/constants";
import { PartialProperty } from "../../interfaces/me";
import { PropertyStackNavigationProp } from "../../navigation/types";
import { Stats } from "../Stats";

interface ComplaintsSectionProps {
  complaints: Complaint[];
  property: PartialProperty;
}

export const ComplaintsSection: React.FC<ComplaintsSectionProps> = ({
  complaints,
  property,
}) => {
  const navigation = useNavigation<PropertyStackNavigationProp>();

  if (!complaints.length) {
    return (
      <Section title="Quejas">
        <Text style={styles.noDataText}>No hay quejas registradas</Text>
      </Section>
    );
  }

  // Separar por estado
  const openComplaints = complaints.filter((c) => c.status === Status.OPEN);
  const inProgressComplaints = complaints.filter(
    (c) => c.status === Status.IN_PROGRESS
  );
  const resolvedComplaints = complaints.filter(
    (c) => c.status === Status.RESOLVED
  );

  return (
    <Section title="Quejas">
      <Card style={styles.summaryCard}>
        <Stats
          items={[
            {
              icon: "clock-outline",
              label: "Abiertas",
              value: openComplaints.length,
              color: theme.colors.primary,
            },
            {
              icon: "clock-fast",
              label: "En Progreso",
              value: inProgressComplaints.length,
              color: theme.colors.warning,
            },
            {
              icon: "check-circle",
              label: "Resueltas",
              value: resolvedComplaints.length,
              color: theme.colors.success,
            },
          ]}
          goTo={{
            label: "Ver las quejas",
            onPress: () =>
              navigation.navigate(PROPERTY_SCREENS.PROPERTY_COMPLAINTS, {
                complaints,
                property,
              }),
          }}
        />
      </Card>
    </Section>
  );
};

const styles = StyleSheet.create({
  summaryCard: {
    flexDirection: "column",
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.md,
  },
  noDataText: {
    textAlign: "center",
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray500,
    padding: theme.spacing.lg,
    fontStyle: "italic",
  },
});
