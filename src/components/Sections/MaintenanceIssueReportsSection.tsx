import { StyleSheet, Text } from "react-native";
import { useNavigation } from "@react-navigation/native";
import { Card, Section } from "../main";
import {
  PartialMaintenanceIssueReport,
  PartialProperty,
} from "../../interfaces/me";
import { theme } from "../../theme";
import { PROPERTY_SCREENS } from "../../navigation/constants";
import { PropertyStackNavigationProp } from "../../navigation/types";
import { Stats } from "../Stats";

interface MaintenanceIssueReportsSectionProps {
  maintenanceIssueReports: PartialMaintenanceIssueReport[];
  property: PartialProperty;
}

export const MaintenanceIssueReportsSection: React.FC<
  MaintenanceIssueReportsSectionProps
> = ({ maintenanceIssueReports, property }) => {
  const navigation = useNavigation<PropertyStackNavigationProp>();

  if (!maintenanceIssueReports.length) {
    return (
      <Section title="Reportes de mantenimiento">
        <Text style={styles.noDataText}>No hay reportes de mantenimiento</Text>
      </Section>
    );
  }

  // Separar por estado
  const openReports = maintenanceIssueReports.filter(
    (r) =>
      r.status.toLowerCase().includes("abierto") ||
      r.status.toLowerCase().includes("open")
  );
  const inProgressReports = maintenanceIssueReports.filter(
    (r) =>
      r.status.toLowerCase().includes("progreso") ||
      r.status.toLowerCase().includes("progress")
  );
  const resolvedReports = maintenanceIssueReports.filter(
    (r) =>
      r.status.toLowerCase().includes("resuelto") ||
      r.status.toLowerCase().includes("resolved") ||
      r.status.toLowerCase().includes("completado")
  );

  return (
    <Section title="Reportes de mantenimiento">
      <Card style={styles.summaryCard}>
        <Stats
          items={[
            {
              icon: "clock-outline",
              label: "Abiertos",
              value: openReports.length,
              color: theme.colors.primary,
            },
            {
              icon: "clock-fast",
              label: "En Progreso",
              value: inProgressReports.length,
              color: theme.colors.warning,
            },
            {
              icon: "check-circle",
              label: "Resueltos",
              value: resolvedReports.length,
              color: theme.colors.success,
            },
          ]}
          goTo={{
            label: "Ver los reportes",
            onPress: () =>
              navigation.navigate(
                PROPERTY_SCREENS.PROPERTY_MAINTENANCE_REPORTS,
                {
                  maintenanceIssueReports,
                  property,
                }
              ),
          }}
        />
      </Card>
    </Section>
  );
};

const styles = StyleSheet.create({
  summaryCard: {
    flexDirection: "column",
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.lg,
  },
  noDataText: {
    textAlign: "center",
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray500,
    padding: theme.spacing.lg,
    fontStyle: "italic",
  },
});
