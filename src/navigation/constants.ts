// Tab Names
export const TAB_NAMES = {
  DASHBOARD: "DashboardTab",
  FACILITIES: "FacilitiesTab",
  PROPERTY: "PropertyTab",
  PAYMENTS: "PaymentsTab",
  ACCOUNT: "AccountTab",
} as const;

// Tab Labels (for display)
export const TAB_LABELS = {
  [TAB_NAMES.DASHBOARD]: "Dashboard",
  [TAB_NAMES.FACILITIES]: "Amenidades",
  [TAB_NAMES.PROPERTY]: "Propiedad",
  [TAB_NAMES.PAYMENTS]: "Pagos",
  [TAB_NAMES.ACCOUNT]: "Cuenta",
} as const;

// Stack Screen Names
export const DASHBOARD_SCREENS = {
  DASHBOARD: "Dashboard",
  CREATE_COMPLAINT: "CreateComplaint",
  CREATE_MAINTENANCE_REPORT: "CreateMaintenanceReport",
  EMERGENCY_NUMBERS: "EmergencyNumbers",
} as const;

export const FACILITIES_SCREENS = {
  FACILITIES_LIST: "FacilitiesList",
  FACILITY_DETAIL: "FacilityDetail",
  CREATE_RESERVATION: "CreateReservation",
} as const;

export const PROPERTY_SCREENS = {
  PROPERTY_DETAIL: "PropertyDetail",
  PROPERTY_RESIDENTS: "PropertyResidents",
  PROPERTY_VEHICLES: "PropertyVehicles",
  PROPERTY_PETS: "PropertyPets",
  PROPERTY_TAGS: "PropertyTags",
  PROPERTY_FINES: "PropertyFines",
  PROPERTY_COMPLAINTS: "PropertyComplaints",
  PROPERTY_MAINTENANCE_REPORTS: "PropertyMaintenanceReports",
  PROPERTY_INFRACTIONS: "PropertyInfractions",
  PROPERTY_RESERVATIONS: "PropertyReservations",
  PROPERTY_MONTHLY_CHARGES: "PropertyMonthlyCharges",
  // Detail screens
  RESERVATION_DETAIL: "ReservationDetail",
  FINE_DETAIL: "FineDetail",
  INFRACTION_DETAIL: "InfractionDetail",
  MONTHLY_CHARGE_DETAIL: "MonthlyChargeDetail",
  COMPLAINT_DETAIL: "ComplaintDetail",
  MAINTENANCE_ISSUE_REPORT_DETAIL: "MaintenanceIssueReportDetail",
} as const;

export const PAYMENTS_SCREENS = {
  PAYMENTS_LIST: "PaymentsList",
  PAYMENT_DETAIL: "PaymentDetail",
} as const;

export const ACCOUNT_SCREENS = {
  PROFILE: "Profile",
  SETTINGS: "Settings",
  EDIT_PROFILE: "EditProfile",
} as const;

// Routes where tab bar should be hidden
export const HIDDEN_TAB_BAR_ROUTES = [
  DASHBOARD_SCREENS.CREATE_COMPLAINT,
  DASHBOARD_SCREENS.CREATE_MAINTENANCE_REPORT,
  FACILITIES_SCREENS.FACILITY_DETAIL,
  FACILITIES_SCREENS.CREATE_RESERVATION,
  PAYMENTS_SCREENS.PAYMENT_DETAIL,
  ACCOUNT_SCREENS.EDIT_PROFILE,
] as const;

// Tab Icons mapping
export const TAB_ICONS = {
  [TAB_NAMES.DASHBOARD]: "collage",
  [TAB_NAMES.FACILITIES]: "spa-outline",
  // [TAB_NAMES.FACILITIES]: "pool",
  // [TAB_NAMES.FACILITIES]: "party-popper",
  [TAB_NAMES.PROPERTY]: "folder-home-outline",
  [TAB_NAMES.PAYMENTS]: "shield-key-outline",
  [TAB_NAMES.ACCOUNT]: "cog-outline",
} as const;

// Screen options
export const MODAL_SCREENS = [DASHBOARD_SCREENS.EMERGENCY_NUMBERS] as const;
