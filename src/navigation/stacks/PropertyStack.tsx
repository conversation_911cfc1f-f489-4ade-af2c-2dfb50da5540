import { createNativeStackNavigator } from "@react-navigation/native-stack";
import { PropertyScreen } from "../../screens/Property/PropertyScreen";
import { PropertyStackParamList } from "../types";
import { PROPERTY_SCREENS } from "../constants";
import { PropertyComplaintsScreen } from "../../screens/Property/PropertyComplaintsScreen";
import { PropertyFinesScreen } from "../../screens/Property/PropertyFinesScreen";
import { PropertyInfractionsScreen } from "../../screens/Property/PropertyInfractionsScreen";
import { PropertyReservationsScreen } from "../../screens/Property/PropertyReservationsScreen";
import { PropertyMaintenanceReportsScreen } from "../../screens/Property/PropertyMaintenanceReportsScreen";
import { PropertyMonthlyChargesScreen } from "../../screens/Property/PropertyMonthlyChargesScreen";
// Detail screens
import {
  ReservationDetailScreen,
  FineDetailScreen,
  InfractionDetailScreen,
  MonthlyChargeDetailScreen,
  ComplaintDetailScreen,
  MaintenanceIssueReportDetailScreen,
} from "../../screens/Details";

const Stack = createNativeStackNavigator<PropertyStackParamList>();

export const PropertyStack: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        animation: "slide_from_right",
      }}
    >
      <Stack.Screen
        name={PROPERTY_SCREENS.PROPERTY_DETAIL}
        component={PropertyScreen}
        options={{
          title: "Mi Propiedad",
        }}
      />
      <Stack.Screen
        name={PROPERTY_SCREENS.PROPERTY_INFRACTIONS}
        component={PropertyInfractionsScreen}
        options={{
          title: "Infracciones",
        }}
      />
      <Stack.Screen
        name={PROPERTY_SCREENS.PROPERTY_FINES}
        component={PropertyFinesScreen}
        options={{
          title: "Multas",
        }}
      />
      <Stack.Screen
        name={PROPERTY_SCREENS.PROPERTY_RESERVATIONS}
        component={PropertyReservationsScreen}
        options={{
          title: "Reservaciones",
        }}
      />
      <Stack.Screen
        name={PROPERTY_SCREENS.PROPERTY_COMPLAINTS}
        component={PropertyComplaintsScreen}
        options={{
          title: "Quejas",
        }}
      />
      <Stack.Screen
        name={PROPERTY_SCREENS.PROPERTY_MAINTENANCE_REPORTS}
        component={PropertyMaintenanceReportsScreen}
        options={{
          title: "Reportes de Mantenimiento",
        }}
      />
      <Stack.Screen
        name={PROPERTY_SCREENS.PROPERTY_MONTHLY_CHARGES}
        component={PropertyMonthlyChargesScreen}
        options={{
          title: "Cargos Mensuales",
        }}
      />
      {/* Detail screens */}
      <Stack.Screen
        name={PROPERTY_SCREENS.RESERVATION_DETAIL}
        component={ReservationDetailScreen}
        options={{
          title: "Detalle de Reservación",
        }}
      />
      <Stack.Screen
        name={PROPERTY_SCREENS.FINE_DETAIL}
        component={FineDetailScreen}
        options={{
          title: "Detalle de Multa",
        }}
      />
      <Stack.Screen
        name={PROPERTY_SCREENS.INFRACTION_DETAIL}
        component={InfractionDetailScreen}
        options={{
          title: "Detalle de Infracción",
        }}
      />
      <Stack.Screen
        name={PROPERTY_SCREENS.MONTHLY_CHARGE_DETAIL}
        component={MonthlyChargeDetailScreen}
        options={{
          title: "Detalle de Cargo",
        }}
      />
      <Stack.Screen
        name={PROPERTY_SCREENS.COMPLAINT_DETAIL}
        component={ComplaintDetailScreen}
        options={{
          title: "Detalle de Queja",
        }}
      />
      <Stack.Screen
        name={PROPERTY_SCREENS.MAINTENANCE_ISSUE_REPORT_DETAIL}
        component={MaintenanceIssueReportDetailScreen}
        options={{
          title: "Detalle de Reporte",
        }}
      />
    </Stack.Navigator>
  );
};

// Export for backward compatibility
export const PropertiesStack = PropertyStack;
